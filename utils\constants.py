"""
常量定义

定义应用程序中使用的常量
"""

# 应用程序信息
APP_NAME = "陶瓷配方计算器"
APP_VERSION = "1.0"
APP_TITLE = f"{APP_NAME} V{APP_VERSION}"

# 文件路径
DEFAULT_MATERIALS_FILE = "oxide_powders_initial.xlsx"
TEMPLATE_FILE = "oxide_powders_template.xlsx"
DATABASE_FILE = "materials.db"

# UI配置
WINDOW_WIDTH = 1600
WINDOW_HEIGHT = 1000
WINDOW_GEOMETRY = f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}"

# 表格配置
TABLE_ROW_HEIGHT = 50
TABLE_HEIGHT = 25

# 颜色配置
COLORS = {
    'primary': '#002EA6',
    'success': '#28a745',
    'info': '#6f42c1',
    'warning': '#fd7e14',
    'danger': '#dc3545',
    'cyan': '#17a2b8',
    'dark_green': '#155724',
    'light_blue': '#007bff',
    'background': '#ffffff',
    'border': '#e9ecef',
    'text': '#495057',
    'even_row': '#F5FAFE',
    'odd_row': '#DCE9F7'
}

# 字体配置
FONTS = {
    'default': ('Microsoft YaHei', 10),
    'bold': ('Microsoft YaHei', 10, 'bold'),
    'large': ('Microsoft YaHei', 12),
    'large_bold': ('Microsoft YaHei', 12, 'bold'),
    'title': ('Microsoft YaHei', 20, 'bold'),
    'subtitle': ('Microsoft YaHei', 16, 'bold'),
    'small': ('Microsoft YaHei', 9)
}



# 数据库字段映射
DB_FIELD_MAPPING = {
    'Z': 'atomic_number',
    'Co.': 'company',
    'Product No.': 'product_no',
    'Element': 'element',
    'Formula': 'formula',
    'Ratio': 'ratio',
    'Excess': 'excess',
    'Mr': 'molecular_weight',
    'CAS': 'cas',
    'Purity': 'purity',
    'Pricing': 'pricing'
}

# 表格列配置
TABLE_COLUMNS = {
    'materials': ['Pick', 'Z', 'Element', 'Formula', 'Ratio', 'Excess', 'Mr', 'CAS', 'Co.', 'Product No.', 'Purity', 'Pricing'],
    'single_result': ["Formula", "Excess", "Mr", "Purity", "Mass"],
    'multi_input': ["序号", "化学式", "总质量(g)", "状态", "操作"]
}

# 列宽配置
COLUMN_WIDTHS = {
    'Pick': 40,
    'Z': 120,
    'Element': 120,
    'Formula': 200,
    'Ratio': 120,
    'Excess': 120,
    'Mr': 150,
    'CAS': 180,
    'Co.': 180,
    'Product No.': 150,
    'Purity': 120,
    'Pricing': 150,
    '序号': 80,
    '化学式': 400,
    '总质量(g)': 150,
    '状态': 120,
    '操作': 100,
    '原料名称': 300,
    '所需质量(g)': 150,
    '百分比(%)': 150,
    'Mass': 150
}

# 数值格式化配置
NUMBER_FORMAT = {
    # Formula、Excess、Mr、Purity的有效位数跟数据库保持一致
    # 使用 :g 格式自动去除尾随零，保持数据库原始精度
    'excess_format': 'g',      # 过量系数：跟数据库保持一致
    'mr_format': 'g',          # 分子量：跟数据库保持一致
    'purity_format': 'g',      # 纯度：跟数据库保持一致
    'mass_decimal_places': 4,  # Mass保留4位小数（0.1mg精度）
}
